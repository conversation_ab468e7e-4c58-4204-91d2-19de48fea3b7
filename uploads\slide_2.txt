Detailed Narrative Report - Slide 2

Content:
## Data Order Provisioning Process

### Overview
- **Preliminary Work & Analysis**: Does not serve as a planning document.

### Order Processing Steps
1. **Order Validation**
   - Check XML and Service Compatibility 
   - **Tools**: iVAPP
   - **Automation**: Yes

2. **Order Provisioning**
   - Facility Assignment
   - **Tools**: iVAPP (Auto)
   - Check IP availability, both Static and Dynamic
   - **Tools**: iVAPP, INAM (Auto)
  
3. **Pre-Provisioning**
   - For Router, ONT & Testing
   - **Tools**: VNM, Delphi (Auto)

4. **Order Completion**
   - **Tools**: iVAPP, SSP (Auto)

5. **Facility Correction**
   - **Tools**: iVAPP, Field Work
   - **Response**: Manual tech assignment if customer is not reachable

6. **Dispatch**
   - **Tools**: Optix Dispatch (Auto)

7. **Activation**
   - Activation Correction
   - **Tools**: iVAPP, Delphi, FSC

8. **Provisioning Correction**
   - **Tools**: iVAPP, FSC, ITCC

### Field Activities
- Technician Assignment
  - **Tools**: Optix Dispatch (Auto)
  
- Field Activity
  - Drop placement and inside wiring
  - **Tools**: Omega

- Ont Activation
  - **Tools**: Omega, Delphi, iVAPP, VNM (Field Technician / Auto)

- Router Activation
  - **Tools**: Omega, HDM, HNM, CPEM (Field Technician / Auto)

### Upselling Opportunity
- **Tools**: Omega, CRMM, SSP, iVAPP (Field Technician)

### Completion and Billing
- Dispatch Completion
  - **Tools**: Omega, Optix Dispatch, iVAPP (Field Technician)
  
- Billing
  - **Tools**: SSP, Vision (Auto)

- Order Correction
  - **Tools**: SSP (SFC, SSP OPS)

- Jeopardy Handle
  - **Tools**: iVAPP, Omega (DRC)

- Billing Correction
  - **Tools**: Vision (SFC, SSP OPS, Revenue Assurance)

### Performance Metrics
- 3% - Customer Notification (Contact Engine, Auto)
- 2% - Technician Enroute (Omega, Auto)
- **Satisfaction Metrics**:
  - 98%, 97%, 99%, 98%, 99%, 85%, 97%, 90%
  
### Additional Activities
- **Router Shipment for Self-Install**
   - **Tools**: iVAPP, CPEM (Auto) 

### Notes
- **Manual Interventions** included where customer engagement is necessary or system automation is not feasible.
