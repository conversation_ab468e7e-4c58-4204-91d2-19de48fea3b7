Detailed Narrative Report - Slide 2

Content:
# Data Order Provisioning Process

## Overview
This document outlines the steps involved in data order provisioning. Please note that it is a preliminary work and analysis, and does not serve as a formal planning document.

### Key Steps in the Order Provisioning Process

1. **Order Validation**
   - XML and Service Compatibility Check
   - Tools: iVAPP
   - Process: Automated

2. **Order Provisioning**
   - Facility Assignment
     - Tools: iVAPP
     - Process: Automated

   - IP Availability Check
     - Tools: iVAPP, INAM
     - Note: Includes Augmentation for Static and Dynamic Assignments
     - Process: Automated

3. **Pre-Provisioning**
   - Preparation for Router, ONT & Testing
   - Tools: VNM, Delphi
   - Process: Automated

4. **Order Completion**
   - Tools: iVAPP, SSP
   - Process: Automated

5. **Facility Correction**
   - Tools: iVAPP, Field Work (OSP, ITCC)
   - Process: Manual

6. **Technician Assignment**
   - Tools: Optix Dispatch
   - Process: Automated

7. **Field Activity**
   - Drop Placement and Inside Wiring
   - Tools: Omega
   - Field Technician Involvement: Required

8. **Activation**
   - **ONT Activation**
     - Tools: Omega, Delphi, iVAPP, VNM
     - Field Technician / Automated

   - **Router Activation**
     - Tools: Omega, HDM, HNM, CPEM
     - Field Technician / Automated

9. **Upsell Opportunities**
   - Tools: Omega, CRMM, SSP, iVAPP
   - Involvement: Field Technician

10. **Dispatch Completion**
    - Tools: Omega, Optix Dispatch, iVAPP
    - Involvement: Field Technician

11. **Billing**
    - Tools: SSP, Vision
    - Process: Automated

12. **Order Correction**
    - Tools: SSP
    - Teams Involved: SFC, SSP OPS

13. **Jeopardy Handling**
    - Tools: iVAPP, Omega, DRC

14. **Billing Correction**
    - Tools: Vision
    - Teams Involved: SFC, SSP OPS, Revenue Assurance

### Additional Notes
- **Customer Notification** 
  - Tool: Contact Engine
  - Process: Automated

- **Router Shipment for Self-Install**
  - Tools: iVAPP, CPEM
  - Process: Automated

### Performance Metrics
- Technician Enroute: 98%, 97%, 99%, 98%, 99%
- Order Completion Rates by Different Steps: 
  - 3%, 2%, 15%
  - Manual interventions noted where applicable: Rajamani/Dilli

### Summary
This structured approach to data order provisioning ensures efficiency and accountability throughout the order fulfillment lifecycle. Each step is tracked, with a mix of automated and manual processes to address potential issues and improve customer satisfaction.
