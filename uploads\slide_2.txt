Detailed Narrative Report - Slide 2

Content:
### Data Order Provisioning Process

#### Overview
- **Note**: Preliminary work and analysis; does not constitute a planning document.

#### Key Steps in Order Provisioning

1. **Order Validation**
   - Compatibility checks (XML, Service)
   - Tools: iVAPP
   - Method: Automated

2. **Order Provisioning**
   - Facility Assignment
     - Tools: iVAPP
     - Method: Automated
   - IP Availability Check 
     - Augmentation for Static and Dynamic
     - Tools: iVAPP, INAM
     - Method: Automated

3. **Pre-Provisioning**
   - For Router, ONT & Testing
   - Tools: VNM, Delphi
   - Method: Automated

4. **Order Completion**
   - Tools: iVAPP, SSP
   - Method: Automated

5. **Facility Correction**
   - Tools: iVAPP, Field Work
   - Manual technician assignment for unreachable customers
   - Tools: OSP, ITCC

6. **Activation**
   - **Order Dispatch**
     - Tools: Optix Dispatch
     - Method: Automated
   - **Technician Assignment**
     - Tools: Optix Dispatch
     - Method: Automated
   - **Field Activity** (Drop placement and inside wiring)
     - Tools: Omega
     - Involvement of Field Technicians

7. **Activation and Equipment Setup**
   - **ONT Activation**
     - Tools: Omega, Delphi, iVAPP, VNM
     - Field Technician - Automated
   - **Router Activation**
     - Tools: Omega, HDM, HNM, CPEM
     - Field Technician - Automated

8. **Sales Opportunities**
   - **Sell One More (Upsell)**
     - Tools: Omega, CRMM, SSP, iVAPP
     - Involvement of Field Technicians

9. **Dispatch Completion**
   - Tools: Omega, Optix Dispatch, iVAPP
   - Involvement of Field Technicians

10. **Billing**
    - Tools: SSP, Vision
    - Method: Automated

11. **Order Correction**
    - Tools: SSP
    - Involvement of SFC, SSP OPS

12. **Jeopardy Handling**
    - Tools: iVAPP, Omega
    - Method: DRC

13. **Billing Correction**
    - Tools: Vision
    - Involvement of SFC, SSP OPS, Revenue Assurance

#### Performance Metrics
- Technician Enroute: 98%
- Customer Notification: 97%
- Router Shipment for Self Install: 99%
- Manual Task Completion: 85%
- Automated Task Completion: 97%

### Summary
This structured approach outlines the complete flow from order validation to billing in the Data Order Provisioning process, highlighting the roles of automated systems, field technicians, and associated tools for each step.
