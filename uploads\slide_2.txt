Detailed Narrative Report - Slide 2

Content:
# Data Order Provisioning Process

---

### Overview
- Preliminary Work & Analysis (Note: Does not constitute a planning document)

---

### Order Processing Stages

1. **Order Validation**
   - XML Service Compatibility
   - Automated via iVAPP

2. **Order Provisioning**
   - Facility Assignment 
     - Automated via iVAPP
   - IP Availability Check 
     - Static and Dynamic
     - Automated via iVAPP, INAM
  
3. **Pre-Provisioning**
   - For Router, ONT & Testing 
   - Automated via VNM, Delphi

4. **Order Completion**
   - Automated via iVAPP, SSP

5. **Facility Correction**
   - Manual via iVAPP, Field Work
   - Technician Assignment (if customer not reachable)
   - Manual via OSP, ITCC

6. **Activation**
   - Automated Corrections via iVAPP, Delphi
   - Technician Assignment 
     - Automated via Optix Dispatch

---

### Field Activities

- **Drop Placement & Inside Wiring**
  - Performed by Field Technicians using Omega

- **ONT Activation**
  - Automated and Manual via Omega, Delphi, iVAPP, VNM

- **Router Activation**
  - Automated and Manual via Omega, HDM, HNM, CPEM
  
- **Upsell Opportunities (Sell One More)**
  - Engaged through Omega, CRMM, SSP, iVAPP
  
- **Dispatch Completion**
  - Managed via Omega, Optix Dispatch, iVAPP

---

### Billing Process

- Order Billing
  - Automated via SSP, Vision
  
- Order Correction
  - Handled by SSP, SFC, SSP OPS

- Jeopardy Handling
  - Managed by iVAPP, Omega, DRC

- Billing Correction
  - Processed through Vision, SFC, SSP OPS, Revenue Assurance

---

### Metrics

- **Technician Enroute:** 98%
- **Customer Notification Automation:** 97%
- **Router Shipment for Self Install Automation:** 99%
- **Order Completion:** 85%
- **Billing Success Rate:** 90%

---

### Notes
- Auto refers to processes that are automated.
- Manual indicates processes that require human intervention.
