import os
import tempfile
import smtplib
import asyncio
import concurrent.futures
from pathlib import Path
from fastapi import Fast<PERSON><PERSON>, UploadFile, Form
from fastapi.responses import JSONResponse
from pptx import Presentation
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import List
from io import Bytes<PERSON>
from PIL import Image
from dotenv import load_dotenv
from openai import OpenAI
try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
 
# Load .env file
load_dotenv()
 
# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
 
app = FastAPI()
 
# TheyDo configuration
theydo_url = "https://app.theydo.com/radiant-digital/frontier-migration/data-hub"
theydo_email = "<EMAIL>"
theydo_password = "5xHqgWTwWivSTAy!"
uploads_dir = Path(__file__).parent / "uploads"
 
# ---------------------------
# TheyDo Upload Function
# ---------------------------
def run_theydo_upload(files_dir: Path):
    async def upload():
        files = list(files_dir.iterdir())
        if not files:
            return False
 
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, args=["--start-maximized", "--disable-web-security"])
            context = await browser.new_context(viewport={"width": 1920, "height": 1080})
            page = await context.new_page()
 
            await page.goto("https://app.theydo.io/login", wait_until="domcontentloaded", timeout=60000)
            await page.wait_for_selector('input[type="text"], input[type="email"], [name="email"]', timeout=20000)
            await asyncio.sleep(2)
 
            # Login
            email_selectors = ['input[type="text"]', 'input[type="email"]', '[name="email"]', '#email']
            for selector in email_selectors:
                try:
                    email_input = await page.wait_for_selector(selector, timeout=5000)
                    if email_input:
                        break
                except:
                    continue
           
            password_input = await page.wait_for_selector('input[type="password"]', timeout=5000)
           
            await email_input.fill(theydo_email)
            await password_input.fill(theydo_password)
            await page.click('button[type="submit"]')
 
            try:
                await page.wait_for_selector('text=Data Hub', timeout=60000)
            except:
                await page.wait_for_url("**/workspace/**", timeout=60000)
 
            await page.goto(theydo_url, wait_until="domcontentloaded", timeout=60000)
            await asyncio.sleep(5)
 
            # Upload files
            print("Looking for upload button...")
            upload_buttons = ['button:has-text("Upload")', 'span:has-text("Upload")', '[role="button"]:has-text("Upload")']
            upload_clicked = False
            for i, selector in enumerate(upload_buttons):
                try:
                    print(f"Trying upload selector {i+1}: {selector}")
                    main_upload_button = await page.wait_for_selector(selector, timeout=3000)
                    if main_upload_button:
                        await main_upload_button.click()
                        print(f"✓ Successfully clicked upload button with selector: {selector}")
                        upload_clicked = True
                        await asyncio.sleep(2)
                        break
                except Exception as e:
                    print(f"✗ Upload selector {i+1} failed: {e}")
                    continue

            if not upload_clicked:
                print("✗ Could not find upload button")
                return False

            print("Waiting for upload dialog...")
            await page.wait_for_selector('[aria-label="Source upload dialog upload content"]', timeout=10000)
            print("✓ Upload dialog appeared")

            file_input = await page.query_selector('input[type="file"][accept="text/plain,text/csv"]')
            if not file_input:
                print("✗ Could not find file input")
                return False

            file_paths = [str(f.resolve()) for f in files]
            print(f"Uploading {len(file_paths)} files: {[f.name for f in files]}")
            await file_input.set_input_files(file_paths)

            print("Waiting for files to appear in table...")
            await page.wait_for_selector('td:not(:has-text("No results."))', timeout=30000)
            print("✓ Files uploaded successfully")
 
            print("Starting assign type process...")

            # Wait for the upload to complete and assign type section to appear
            try:
                await page.wait_for_selector('text="Assign type to uploaded sources"', timeout=20000)
                print("✓ Found 'Assign type to uploaded sources' section")
                await asyncio.sleep(3)
            except Exception as e:
                print(f"✗ Could not find assign type section: {e}")

            # Look for the "+ Assign type" button specifically
            assign_type_clicked = False
            assign_type_selectors = [
                'button:has-text("+ Assign type")',
                'button:has-text("Assign type")',
                'text="+ Assign type"',
                'text="Assign type"',
                '[aria-label*="Assign type"]',
                'button[title*="Assign type"]'
            ]

            for i, selector in enumerate(assign_type_selectors):
                try:
                    print(f"Trying assign type selector {i+1}: {selector}")
                    assign_button = await page.wait_for_selector(selector, timeout=8000)
                    if assign_button:
                        await assign_button.click()
                        print(f"✓ Successfully clicked assign type button with selector: {selector}")
                        assign_type_clicked = True
                        await asyncio.sleep(2)
                        break
                except Exception as e:
                    print(f"✗ Assign type selector {i+1} failed: {e}")
                    continue

            if not assign_type_clicked:
                print("✗ Could not click any assign type button")

            # Now look for Interview option in dropdown/menu
            interview_selected = False
            if assign_type_clicked:
                interview_selectors = [
                    'text="Interview"',
                    'button:has-text("Interview")',
                    '[role="option"]:has-text("Interview")',
                    'li:has-text("Interview")',
                    'div:has-text("Interview")',
                    '[data-value="Interview"]',
                    '.dropdown-item:has-text("Interview")'
                ]

                for i, selector in enumerate(interview_selectors):
                    try:
                        print(f"Trying interview selector {i+1}: {selector}")
                        interview_option = await page.wait_for_selector(selector, timeout=8000)
                        if interview_option:
                            await interview_option.click()
                            print(f"✓ Successfully selected Interview with selector: {selector}")
                            interview_selected = True
                            await asyncio.sleep(2)
                            break
                    except Exception as e:
                        print(f"✗ Interview selector {i+1} failed: {e}")
                        continue

            if not interview_selected:
                print("✗ Could not select Interview option")

            # Click Save button
            save_clicked = False
            save_selectors = [
                'button:has-text("Save")',
                'button[type="submit"]',
                'text="Save"',
                '[data-testid*="save"]',
                '.btn-primary:has-text("Save")',
                '.btn:has-text("Save")'
            ]

            for i, selector in enumerate(save_selectors):
                try:
                    print(f"Trying save selector {i+1}: {selector}")
                    save_button = await page.wait_for_selector(selector, timeout=10000)
                    if save_button:
                        await save_button.click()
                        print(f"✓ Successfully clicked Save with selector: {selector}")
                        save_clicked = True
                        await asyncio.sleep(3)
                        break
                except Exception as e:
                    print(f"✗ Save selector {i+1} failed: {e}")
                    continue

            if not save_clicked:
                print("✗ Could not click Save button")

            # Click Extract quotes button
            extract_clicked = False
            extract_selectors = [
                'button:has-text("Extract quotes")',
                'text="Extract quotes"',
                '[data-testid*="extract"]',
                '.btn:has-text("Extract quotes")',
                'button[type="button"]:has-text("Extract quotes")',
                '[aria-label*="Extract quotes"]'
            ]

            for i, selector in enumerate(extract_selectors):
                try:
                    print(f"Trying extract quotes selector {i+1}: {selector}")
                    extract_button = await page.wait_for_selector(selector, timeout=15000)
                    if extract_button:
                        await extract_button.click()
                        print(f"✓ Successfully clicked Extract quotes with selector: {selector}")
                        extract_clicked = True
                        await asyncio.sleep(3)
                        break
                except Exception as e:
                    print(f"✗ Extract quotes selector {i+1} failed: {e}")
                    continue

            if not extract_clicked:
                print("✗ Could not click Extract quotes button")

            # Final status
            if assign_type_clicked and interview_selected and save_clicked and extract_clicked:
                print("🎉 SUCCESS: All steps completed successfully!")
            else:
                print(f"⚠️ PARTIAL SUCCESS: Assign type: {assign_type_clicked}, Interview: {interview_selected}, Save: {save_clicked}, Extract: {extract_clicked}")
 
            print("Waiting 5 seconds before closing browser...")
            await asyncio.sleep(5)
            await browser.close()
            print("✓ Browser closed successfully")
            return True
   
    try:
        if os.name == 'nt':
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        return loop.run_until_complete(upload())
    except Exception as e:
        print(f"TheyDo upload failed: {e}")
        return False
    finally:
        loop.close()
 
# ---------------------------
# Extract text using OpenAI (vision or text refinement)
# ---------------------------
def extract_text_with_openai(text_or_image: bytes, is_image: bool = False) -> str:
    try:
        if is_image:
            # Vision model (image input)
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "Extract all visible text except white font with red background from this slide image accurately,and Create a detailed narrative report describing the data from the photo. Write it as a detailed process and work flow report but only in a richtext format."}
                ],
                modalities=["text", "image"],
                input=[
                    {"role": "user", "content": [
                        {"type": "input_text", "text": "Extract all text from this slide."},
                        {"type": "input_image", "image_data": text_or_image}
                    ]}
                ]
            )
            return response.choices[0].message.content
        else:
            # Refine plain extracted text
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "Clean and structure the slide text."},
                    {"role": "user", "content": text_or_image}
                ]
            )
            return response.choices[0].message.content
    except Exception as e:
        return f"[OpenAI Extraction Failed: {e}]"
 
# ---------------------------
# Extract text from PPT slides
# ---------------------------
def ppt_to_text_files(ppt_path: str, output_dir: str) -> List[str]:
    prs = Presentation(ppt_path)
    output_files = []
 
    for i, slide in enumerate(prs.slides, start=1):
        text_content = []
 
        # Extract from text frames
        for shape in slide.shapes:
            if shape.has_text_frame:
                for paragraph in shape.text_frame.paragraphs:
                    for run in paragraph.runs:
                        if run.text.strip():
                            text_content.append(run.text.strip())
 
            # Extract from tables
            if shape.has_table:
                for row in shape.table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text_content.append(cell.text.strip())
 
            # Extract from grouped shapes
            if shape.shape_type == 6:  # group
                for shp in shape.shapes:
                    if shp.has_text_frame:
                        for paragraph in shp.text_frame.paragraphs:
                            for run in paragraph.runs:
                                if run.text.strip():
                                    text_content.append(run.text.strip())
 
        slide_text = "\n".join(text_content).strip()
 
        if slide_text:
            refined_text = extract_text_with_openai(slide_text, is_image=False)
        else:
            # fallback: create blank image & try Vision (in real case export slide as PNG)
            img = Image.new("RGB", (1280, 720), color="white")
            buf = BytesIO()
            img.save(buf, format="PNG")
            buf.seek(0)
            refined_text = extract_text_with_openai(buf.getvalue(), is_image=True)
 
        narrative = f"Detailed Narrative Report - Slide {i}\n\nContent:\n{refined_text}\n"
 
        file_path = os.path.join(output_dir, f"slide_{i}.txt")
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(narrative)
        output_files.append(file_path)
 
    return output_files
 
# ---------------------------
# Send email with attachments via Outlook SMTP
# ---------------------------
def send_email_outlook(sender_email: str, password: str, receiver_email: str, files: List[str]):
    smtp_server = "smtp.office365.com"
    port = 587
 
    msg = MIMEMultipart()
    msg["From"] = sender_email
    msg["To"] = receiver_email
    msg["Subject"] = "TheyDo Narrative Reports"
 
    body = "Please find attached TheyDo-compatible narrative reports from PPT."
    msg.attach(MIMEText(body, "plain"))
 
    # Save attachments to uploads folder
    uploads_dir.mkdir(exist_ok=True)
    for file in files:
        with open(file, "r", encoding="utf-8") as f:
            content = f.read()
            attachment = MIMEText(content, "plain")
            attachment.add_header("Content-Disposition", "attachment", filename=os.path.basename(file))
            msg.attach(attachment)
           
            # Save to uploads folder for TheyDo
            upload_file_path = uploads_dir / os.path.basename(file)
            with open(upload_file_path, "w", encoding="utf-8") as upload_file:
                upload_file.write(content)
 
    with smtplib.SMTP(smtp_server, port) as server:
        server.starttls()
        server.login(sender_email, password)
        server.sendmail(sender_email, receiver_email, msg.as_string())
 
# ---------------------------
# FastAPI Endpoint
# ---------------------------
@app.post("/convert_ppt/")
async def convert_ppt(
    file: UploadFile,
    sender_email: str = Form(...),
    password: str = Form(...),
    receiver_email: str = Form(...)
):
    try:
        with tempfile.TemporaryDirectory() as tmpdir:
            ppt_path = os.path.join(tmpdir, file.filename)
            with open(ppt_path, "wb") as f:
                f.write(await file.read())
 
            txt_files = ppt_to_text_files(ppt_path, tmpdir)
 
            # Send email with attachments (also saves to uploads folder)
            send_email_outlook(sender_email, password, receiver_email, txt_files)
           
            # Try to upload to TheyDo
            theydo_success = False
            if PLAYWRIGHT_AVAILABLE:
                try:
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(run_theydo_upload, uploads_dir)
                        theydo_success = future.result(timeout=300)  # 5 minute timeout
                except Exception as e:
                    print(f"TheyDo upload error: {e}")
            else:
                print("Playwright not available - skipping TheyDo upload")
           
            message = "PPT converted and emailed successfully."
            if theydo_success:
                message = "PPT converted, emailed, and uploaded to TheyDo successfully."
            else:
                message += " TheyDo upload failed - files saved to uploads folder."
 
            return JSONResponse({
                "message": message,
                "files": [os.path.basename(f) for f in txt_files],
                "theydo_uploaded": theydo_success,
                "ai_provider": "openai"
            })
    except Exception as e:
        return JSONResponse({"error": str(e)}, status_code=500)